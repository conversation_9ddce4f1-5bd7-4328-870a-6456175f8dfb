import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/server'

export async function GET(
  request: NextRequest,
  { params }: { params: { entryId: string } }
) {
  try {
    const { entryId } = await params
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')
    
    const supabase = await createSupabaseServerClient()

    // Get public backers using the database function
    const { data: backers, error } = await supabase
      .rpc('get_public_story_venture_backers', { 
        entry_id: entryId,
        limit_count: Math.min(limit, 50) // Cap at 50 for performance
      })

    if (error) {
      console.error('Error fetching story venture backers:', error)
      return NextResponse.json(
        { error: 'Failed to fetch backers' },
        { status: 500 }
      )
    }

    return NextResponse.json({ backers: backers || [] })
  } catch (error) {
    console.error('Unexpected error in story venture backers:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
