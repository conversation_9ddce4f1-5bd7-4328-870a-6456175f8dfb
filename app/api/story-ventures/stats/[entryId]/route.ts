import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/server'

export async function GET(
  request: NextRequest,
  { params }: { params: { entryId: string } }
) {
  try {
    const { entryId } = await params
    const supabase = await createSupabaseServerClient()

    // Get funding stats using the database function
    const { data: stats, error } = await supabase
      .rpc('get_story_venture_stats', { entry_id: entryId })
      .single()

    if (error) {
      console.error('Error fetching story venture stats:', error)
      return NextResponse.json(
        { error: 'Failed to fetch funding stats' },
        { status: 500 }
      )
    }

    if (!stats) {
      return NextResponse.json(
        { error: 'Entry not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Unexpected error in story venture stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
