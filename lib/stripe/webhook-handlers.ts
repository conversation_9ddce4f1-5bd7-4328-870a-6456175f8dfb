// Add this to your existing webhook handler
import { createSupabaseClient } from '@/lib/supabase/client'

export async function handleNotificationCreditsPayment(session: { customer_details?: { email?: string }; amount_total: number }) {
  const supabase = createSupabaseClient()
  
  // Extract customer email from session
  const customerEmail = session.customer_details?.email
  if (!customerEmail) {
    console.error('No customer email in session')
    return
  }

  // Find user by email
  const { data: user, error: userError } = await supabase
    .from('users')
    .select('id, notification_credits')
    .eq('email', customerEmail)
    .single()

  if (userError || !user) {
    console.error('User not found for email:', customerEmail)
    return
  }

  // Calculate credits based on amount paid
  // $0.05 per credit = 5 cents per credit
  const amountPaid = session.amount_total // in cents
  const creditsToAdd = Math.floor(amountPaid / 5) // 5 cents per credit

  if (creditsToAdd <= 0) {
    console.error('Invalid payment amount:', amountPaid)
    return
  }

  // Add credits to user account
  const { error: updateError } = await supabase
    .from('users')
    .update({
      notification_credits: (user.notification_credits || 0) + creditsToAdd
    })
    .eq('id', user.id)

  if (updateError) {
    console.error('Error updating user credits:', updateError)
    return
  }

  console.log(`Added ${creditsToAdd} credits to user ${user.id}`)

  // Optional: Send confirmation email
  // await sendCreditsConfirmationEmail(customerEmail, creditsToAdd)
}

export async function handleStoryVenturePayment(paymentIntent: any) {
  const supabase = createSupabaseClient()

  try {
    const metadata = paymentIntent.metadata
    const isStoryVenture = metadata.is_story_venture === 'true'

    if (!isStoryVenture) {
      return // Not a Story Venture payment
    }

    const entryId = metadata.entry_id
    const payerId = metadata.payer_id
    const writerId = metadata.writer_id
    const amount = paymentIntent.amount
    const message = metadata.donation_message

    if (!entryId || !payerId || !writerId) {
      console.error('Missing required metadata for Story Venture payment:', metadata)
      return
    }

    // Get payer information for display name
    const { data: payer } = await supabase
      .from('users')
      .select('name')
      .eq('id', payerId)
      .single()

    // Insert backer record
    const { error: backerError } = await supabase
      .from('story_venture_backers')
      .insert({
        diary_entry_id: entryId,
        backer_id: payerId,
        backer_name: payer?.name || 'Anonymous',
        amount_cents: amount,
        message: message || null,
        stripe_payment_intent_id: paymentIntent.id,
        show_publicly: true // Default to public, can be changed later
      })

    if (backerError) {
      console.error('Error inserting Story Venture backer:', backerError)
      return
    }

    // Update the diary entry's funding_raised_cents
    const { data: currentEntry, error: fetchError } = await supabase
      .from('diary_entries')
      .select('funding_raised_cents')
      .eq('id', entryId)
      .single()

    if (fetchError) {
      console.error('Error fetching current funding amount:', fetchError)
      return
    }

    const newRaisedAmount = (currentEntry.funding_raised_cents || 0) + amount

    const { error: updateError } = await supabase
      .from('diary_entries')
      .update({ funding_raised_cents: newRaisedAmount })
      .eq('id', entryId)

    if (updateError) {
      console.error('Error updating funding amount:', updateError)
      return
    }

    console.log(`Story Venture payment processed: $${amount/100} for entry ${entryId}`)

    // Optional: Send notification to writer about new backer
    // await sendNewBackerNotification(writerId, entryId, amount, payer?.name)

  } catch (error) {
    console.error('Error processing Story Venture payment:', error)
  }
}
