"use client"

import { useState } from "react"

interface DiaryInteractionsProps {
  canReadFull: boolean
  entryId: string
  writerId: string
  isStoryVenture?: boolean
  storyVentureDescription?: string
}

function formatPrice(cents: number) {
  return `$${(cents / 100).toFixed(2)}`
}

export function DiaryInteractions({ canReadFull, entryId, writerId, isStoryVenture, storyVentureDescription }: DiaryInteractionsProps) {
  const [loading, setLoading] = useState(false)
  const [, setShowCustomTip] = useState(false)
  const [customAmount, setCustomAmount] = useState("")
  const [donationMessage, setDonationMessage] = useState("")

  const handleTip = async (amount: number) => {
    setLoading(true)

    try {
      const response = await fetch('/api/stripe/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          writerId,
          paymentType: 'donation',
          amount,
          message: donationMessage.trim() || undefined,
          entryId: isStoryVenture ? entryId : undefined,
          isStoryVenture: isStoryVenture || false
        })
      })

      const data = await response.json()

      if (data.url) {
        window.location.href = data.url
      } else {
        console.error('No checkout URL returned:', data.error)
        alert('Failed to create donation checkout. Please try again.')
      }
    } catch (error) {
      console.error('Error creating donation checkout:', error)
      alert('Failed to process donation. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleCustomTip = async () => {
    const amount = parseFloat(customAmount)
    if (isNaN(amount) || amount < 1) {
      alert('Please enter a valid amount (minimum $1.00)')
      return
    }

    await handleTip(Math.round(amount * 100)) // Convert to cents
  }

  const tipAmounts = [500, 1000, 2500, 5000] // $5, $10, $25, $50

  return (
    <>
      {/* Tip Section */}
      {canReadFull && (
        <div className="bg-gradient-to-br from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-6 shadow-sm mb-8">
          <h3 className="text-lg font-serif mb-4 text-purple-800">
            💝 Fund This Story
          </h3>
          {isStoryVenture && storyVentureDescription ? (
            <p className="text-gray-600 text-sm mb-4">
              {storyVentureDescription}
            </p>
          ) : (
            <p className="text-gray-600 text-sm mb-4">
              Show your appreciation with a donation. Writer keeps 95% of your contribution.
            </p>
          )}

          {/* Optional Message */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Optional message (will be shared with the writer)
            </label>
            <textarea
              value={donationMessage}
              onChange={(e) => setDonationMessage(e.target.value)}
              placeholder="Thank you for this amazing story..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm text-gray-900 placeholder-gray-400"
              rows={2}
              maxLength={200}
            />
          </div>

          {/* Quick Amount Buttons */}
          <div className="flex gap-2 flex-wrap mb-4">
            {tipAmounts.map((amount) => (
              <button
                key={amount}
                onClick={() => handleTip(amount)}
                disabled={loading}
                className="bg-purple-100 text-purple-700 px-4 py-2 rounded-lg font-medium hover:bg-purple-200 transition-colors text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? '...' : formatPrice(amount)}
              </button>
            ))}
          </div>

          {/* Custom Amount */}
          <div className="border-t pt-4">
            <div className="flex gap-2 items-end">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Custom amount
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                  <input
                    type="number"
                    value={customAmount}
                    onChange={(e) => setCustomAmount(e.target.value)}
                    placeholder="5.00"
                    min="1"
                    step="0.01"
                    className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm text-gray-900 placeholder-gray-400"
                  />
                </div>
              </div>
              <button
                onClick={handleCustomTip}
                disabled={loading || !customAmount}
                className="bg-purple-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-purple-700 transition-colors text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Processing...' : 'Donate'}
              </button>
            </div>
          </div>

          <p className="text-xs text-gray-500 mt-3">
            Secure payment powered by Stripe. You&apos;ll be redirected to complete your donation.
          </p>
        </div>
      )}
    </>
  )
}
