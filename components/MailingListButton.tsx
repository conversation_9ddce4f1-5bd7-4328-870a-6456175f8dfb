"use client"

import { useState } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import Link from "next/link"

interface MailingListButtonProps {
  creatorId: string
  creatorName: string
  customUrl?: string | null
  variant?: 'button' | 'link'
  className?: string
}

export function MailingListButton({ 
  creatorId, 
  creatorName, 
  customUrl,
  variant = 'button',
  className = ""
}: MailingListButtonProps) {
  const [showModal, setShowModal] = useState(false)
  const [email, setEmail] = useState("")
  const [name, setName] = useState("")
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const [error, setError] = useState("")

  const supabase = createSupabaseClient()
  const mailingListUrl = `/${customUrl || `u/${creatorId}`}/mailing-list`

  const handleQuickSubscribe = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!email.trim()) {
      setError("Email is required")
      return
    }

    setLoading(true)
    setError("")

    try {
      const response = await fetch('/api/mailing-list/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          creatorId,
          email: email.trim(),
          name: name.trim()
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to subscribe')
      }

      setSuccess(true)
      setEmail("")
      setName("")
    } catch (err) {
      console.error("Subscription error:", err)
      setError(err instanceof Error ? err.message : "Failed to subscribe")
    } finally {
      setLoading(false)
    }
  }

  if (variant === 'link') {
    return (
      <Link
        href={mailingListUrl}
        className={`inline-flex items-center gap-2 text-purple-600 hover:text-purple-700 font-medium ${className}`}
      >
        <span>📧</span>
        Join Mailing List
      </Link>
    )
  }

  return (
    <>
      <button
        onClick={() => setShowModal(true)}
        className={`inline-flex items-center gap-2 bg-purple-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-purple-700 transition-colors ${className}`}
      >
        <span>📧</span>
        Join Mailing List
      </button>

      {/* Quick Subscribe Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl max-w-md w-full p-6">
            {success ? (
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">✅</span>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  You're Subscribed!
                </h3>
                <p className="text-gray-600 mb-6">
                  You'll receive notifications when {creatorName} sends updates.
                </p>
                <div className="space-y-3">
                  <button
                    onClick={() => setShowModal(false)}
                    className="w-full bg-purple-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-purple-700 transition-colors"
                  >
                    Close
                  </button>
                  <Link
                    href={mailingListUrl}
                    className="block w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-colors text-center"
                    onClick={() => setShowModal(false)}
                  >
                    View Mailing List Page
                  </Link>
                </div>
              </div>
            ) : (
              <>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-bold text-gray-900">
                    Join {creatorName}'s Mailing List
                  </h3>
                  <button
                    onClick={() => setShowModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ✕
                  </button>
                </div>

                <form onSubmit={handleQuickSubscribe} className="space-y-4">
                  <div>
                    <label htmlFor="modal-email" className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address *
                    </label>
                    <input
                      id="modal-email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="modal-name" className="block text-sm font-medium text-gray-700 mb-2">
                      Name (Optional)
                    </label>
                    <input
                      id="modal-name"
                      type="text"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      placeholder="Your name"
                    />
                  </div>

                  {error && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                      <p className="text-red-600 text-sm">{error}</p>
                    </div>
                  )}

                  <div className="flex gap-3">
                    <button
                      type="button"
                      onClick={() => setShowModal(false)}
                      className="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={loading}
                      className="flex-1 bg-purple-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-purple-700 transition-colors disabled:opacity-50"
                    >
                      {loading ? 'Subscribing...' : 'Subscribe'}
                    </button>
                  </div>
                </form>

                <div className="mt-4 text-center">
                  <Link
                    href={mailingListUrl}
                    className="text-sm text-purple-600 hover:text-purple-700"
                    onClick={() => setShowModal(false)}
                  >
                    View full mailing list page →
                  </Link>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </>
  )
}
