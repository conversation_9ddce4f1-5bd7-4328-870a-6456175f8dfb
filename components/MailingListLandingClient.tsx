"use client"

import { useState } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import Image from "next/image"
import Link from "next/link"

interface Creator {
  id: string
  name: string
  bio: string | null
  profile_picture_url: string | null
  avatar: string | null
  custom_url: string | null
  role: string
  hide_subscriber_count: boolean
}

interface MailingListLandingClientProps {
  creator: Creator
  subscriberCount: number
  showSubscriberCount: boolean
}

export function MailingListLandingClient({ 
  creator, 
  subscriberCount, 
  showSubscriberCount 
}: MailingListLandingClientProps) {
  const [email, setEmail] = useState("")
  const [name, setName] = useState("")
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const [error, setError] = useState("")

  const supabase = createSupabaseClient()

  const handleSubscribe = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!email.trim()) {
      setError("Email is required")
      return
    }

    setLoading(true)
    setError("")

    try {
      // Check if already subscribed
      const { data: existing } = await supabase
        .from("creator_notification_subscriptions")
        .select("id")
        .eq("creator_id", creator.id)
        .eq("subscriber_email", email.trim().toLowerCase())
        .single()

      if (existing) {
        setError("You're already subscribed to this creator's notifications!")
        setLoading(false)
        return
      }

      // Get current user if logged in
      const { data: { user } } = await supabase.auth.getUser()

      // Subscribe to notifications
      const { error: subscribeError } = await supabase
        .from("creator_notification_subscriptions")
        .insert({
          creator_id: creator.id,
          subscriber_id: user?.id || null,
          subscriber_email: email.trim().toLowerCase(),
          subscriber_name: name.trim() || null,
          notification_preferences: { email: true, push: true }
        })

      if (subscribeError) {
        throw subscribeError
      }

      setSuccess(true)
      setEmail("")
      setName("")
    } catch (err) {
      console.error("Subscription error:", err)
      setError("Failed to subscribe. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  const profileImage = creator.profile_picture_url || creator.avatar

  if (success) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50 flex items-center justify-center px-4">
        <div className="max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-2xl">✅</span>
          </div>
          
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            You're All Set!
          </h1>
          
          <p className="text-gray-600 mb-6">
            Welcome to {creator.name}'s notification list! You'll receive updates when they send out notifications.
          </p>

          <div className="space-y-3">
            <Link
              href={`/${creator.custom_url || `u/${creator.id}`}`}
              className="block w-full bg-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-purple-700 transition-colors"
            >
              Visit {creator.name}'s Profile
            </Link>
            
            <Link
              href="/discover"
              className="block w-full bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors"
            >
              Discover More Creators
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-100">
        <div className="max-w-4xl mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="text-xl font-bold text-gray-900">
            OnlyDiary
          </Link>
          <Link
            href={`/${creator.custom_url || `u/${creator.id}`}`}
            className="text-purple-600 hover:text-purple-700 font-medium"
          >
            View Profile
          </Link>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-2xl mx-auto px-4 py-12">
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
          {/* Creator Header */}
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 px-8 py-12 text-center text-white">
            <div className="flex flex-col items-center">
              {profileImage ? (
                <Image
                  src={profileImage}
                  alt={creator.name}
                  width={80}
                  height={80}
                  className="rounded-full border-4 border-white/20 mb-4"
                />
              ) : (
                <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mb-4">
                  <span className="text-2xl font-bold">
                    {creator.name.charAt(0).toUpperCase()}
                  </span>
                </div>
              )}
              
              <h1 className="text-3xl font-bold mb-2">
                Join {creator.name}'s Notification List
              </h1>

              <p className="text-xl text-white/90 mb-4">
                Get email updates when they publish new content
              </p>

              {showSubscriberCount && (
                <div className="bg-white/20 rounded-full px-4 py-2">
                  <span className="text-sm font-medium">
                    {subscriberCount} {subscriberCount === 1 ? 'subscriber' : 'subscribers'}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Subscription Form */}
          <div className="p-8">
            {creator.bio && (
              <div className="mb-8 text-center">
                <p className="text-gray-600 text-lg leading-relaxed">
                  {creator.bio}
                </p>
              </div>
            )}

            <form onSubmit={handleSubscribe} className="space-y-6">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address *
                </label>
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  Name (Optional)
                </label>
                <input
                  id="name"
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="Your name"
                />
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-red-600 text-sm">{error}</p>
                </div>
              )}

              <button
                type="submit"
                disabled={loading}
                className="w-full bg-purple-600 text-white px-6 py-4 rounded-lg font-medium hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-lg"
              >
                {loading ? 'Subscribing...' : 'Get Email Updates'}
              </button>
            </form>

            <div className="mt-8 text-center">
              <p className="text-sm text-gray-500">
                You'll receive email notifications when {creator.name} publishes new content or sends updates.
                <br />
                You can unsubscribe at any time.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
