"use client"

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import Image from "next/image"
import Link from "next/link"

interface Creator {
  id: string
  name: string
  bio: string | null
  profile_picture_url: string | null
  avatar: string | null
  custom_url: string | null
  role: string
  hide_subscriber_count: boolean
}

interface MailingListLandingClientProps {
  creator: Creator
  subscriberCount: number
  showSubscriberCount: boolean
}

export function MailingListLandingClient({ 
  creator, 
  subscriberCount, 
  showSubscriberCount 
}: MailingListLandingClientProps) {
  const [email, setEmail] = useState("")
  const [name, setName] = useState("")
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const [error, setError] = useState("")
  const [user, setUser] = useState<any>(null)
  const [checkingAuth, setCheckingAuth] = useState(true)

  const supabase = createSupabaseClient()

  useEffect(() => {
    checkAuthStatus()
  }, [])

  const checkAuthStatus = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
    } catch (err) {
      console.error("Auth check error:", err)
    } finally {
      setCheckingAuth(false)
    }
  }

  const handleQuickSubscribe = async () => {
    if (!user) return

    setLoading(true)
    setError("")

    try {
      // Check if already subscribed
      const { data: existing } = await supabase
        .from("creator_notification_subscriptions")
        .select("id")
        .eq("creator_id", creator.id)
        .eq("subscriber_id", user.id)
        .single()

      if (existing) {
        setError("You're already subscribed to this creator's notifications!")
        setLoading(false)
        return
      }

      // Subscribe to notifications
      const { error: subscribeError } = await supabase
        .from("creator_notification_subscriptions")
        .insert({
          creator_id: creator.id,
          subscriber_id: user.id,
          subscriber_email: user.email,
          subscriber_name: user.user_metadata?.name || null,
          notification_preferences: { email: true, push: true }
        })

      if (subscribeError) {
        throw subscribeError
      }

      setSuccess(true)
    } catch (err) {
      console.error("Subscription error:", err)
      setError("Failed to subscribe. Please try again.")
    } finally {
      setLoading(false)
    }
  }



  const profileImage = creator.profile_picture_url || creator.avatar

  if (success) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50 flex items-center justify-center px-4">
        <div className="max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-2xl">✅</span>
          </div>
          
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            You're All Set!
          </h1>
          
          <p className="text-gray-600 mb-6">
            Welcome to {creator.name}'s notification list! You'll receive updates when they send out notifications.
          </p>

          <div className="space-y-3">
            <Link
              href={`/${creator.custom_url || `u/${creator.id}`}`}
              className="block w-full bg-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-purple-700 transition-colors"
            >
              Visit {creator.name}'s Profile
            </Link>
            
            <Link
              href="/discover"
              className="block w-full bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors"
            >
              Discover More Creators
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-100">
        <div className="max-w-4xl mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="text-xl font-bold text-gray-900">
            OnlyDiary
          </Link>
          <Link
            href={`/${creator.custom_url || `u/${creator.id}`}`}
            className="text-purple-600 hover:text-purple-700 font-medium"
          >
            View Profile
          </Link>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-2xl mx-auto px-4 py-12">
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
          {/* Creator Header */}
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 px-8 py-12 text-center text-white">
            <div className="flex flex-col items-center">
              {profileImage ? (
                <Image
                  src={profileImage}
                  alt={creator.name}
                  width={80}
                  height={80}
                  className="rounded-full border-4 border-white/20 mb-4"
                />
              ) : (
                <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mb-4">
                  <span className="text-2xl font-bold">
                    {creator.name.charAt(0).toUpperCase()}
                  </span>
                </div>
              )}
              
              <h1 className="text-3xl font-bold mb-2">
                Join {creator.name}'s Update List
              </h1>

              <p className="text-xl text-white/90 mb-4">
                Get push notifications when they publish new content
              </p>

              {showSubscriberCount && (
                <div className="bg-white/20 rounded-full px-4 py-2">
                  <span className="text-sm font-medium">
                    {subscriberCount} {subscriberCount === 1 ? 'subscriber' : 'subscribers'}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Subscription Content */}
          <div className="p-8">
            {creator.bio && (
              <div className="mb-8 text-center">
                <p className="text-gray-600 text-lg leading-relaxed">
                  {creator.bio}
                </p>
              </div>
            )}

            {checkingAuth ? (
              <div className="text-center py-8">
                <div className="animate-pulse">
                  <div className="h-12 bg-gray-200 rounded-lg w-full mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3 mx-auto"></div>
                </div>
              </div>
            ) : user ? (
              /* Simple opt-in for logged-in users */
              <div className="text-center">
                <p className="text-gray-600 mb-6">
                  Get notified when {creator.name} publishes new content or sends updates.
                </p>

                {error && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <p className="text-red-600 text-sm">{error}</p>
                  </div>
                )}

                <button
                  onClick={handleQuickSubscribe}
                  disabled={loading}
                  className="w-full bg-purple-600 text-white px-6 py-4 rounded-lg font-medium hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-lg"
                >
                  {loading ? 'Joining...' : 'Join Update List'}
                </button>

                <div className="mt-6 text-center">
                  <p className="text-sm text-gray-500">
                    You'll receive email notifications when {creator.name} publishes new content or sends updates.
                  </p>
                </div>
              </div>
            ) : (
              /* Login prompt for non-logged-in users */
              <div className="text-center">
                <p className="text-gray-600 mb-6">
                  Sign in to subscribe to {creator.name}'s updates and get notified when they publish new content.
                </p>

                <div className="space-y-3">
                  <Link
                    href="/login"
                    className="block w-full bg-purple-600 text-white px-6 py-4 rounded-lg font-medium hover:bg-purple-700 transition-colors text-lg"
                  >
                    Sign In to Join Update List
                  </Link>

                  <Link
                    href="/signup"
                    className="block w-full bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors"
                  >
                    Create Account
                  </Link>
                </div>

                <div className="mt-6 text-center">
                  <p className="text-sm text-gray-500">
                    Join OnlyDiary to follow your favorite creators and get email updates.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
